<?php
class ModelExtensionModuleMultiFeedSyncer extends Model {

    // --- Начало: Константи за пакетна обработка ---
    /**
     * Размер на порцията за пакетно вмъкване на продукти в oc_product и свързаните таблици.
     * По-малките стойности намаляват риска от препълване на паметта и твърде големи SQL заявки,
     * но могат да увеличат общото време за изпълнение поради повече на брой заявки.
     */
    const MFS_BATCH_INSERT_CHUNK_SIZE = 50; // Може да се настрои според нуждите (напр. 50-100)

    /**
     * Размер на порцията за пакетно актуализиране на продукти (цена/количество) с CASE WHEN.
     * Тази операция е по-ефективна, затова може да позволи по-големи порции.
     */
    const MFS_BATCH_UPDATE_CHUNK_SIZE = 150; // Може да се настрои (напр. 100-200)

    /**
     * Време за изчакване (в секунди) между обработката на отделните порции.
     * Полезно за намаляване на натоварването на сървъра при много големи синхронизации.
     * Стойност 0 означава без изчакване.
     */
    const MFS_SLEEP_BETWEEN_CHUNKS = 1; // Променете на 1 или повече, ако е необходимо
    // --- Край: Константи за пакетна обработка ---



    private $log; // Уверете се, че $this->log се инициализира коректно в конструктора на модела или където е подходящо
    private $test_mode = false;
    private $current_dummy_product_id_counter = 100000; // Започваме от по-високо число за уникалност
    private $current_dummy_general_id_counter = 700000;

    public function __construct($registry) {
        parent::__construct($registry);
        $this->log = new Log('multi_feed_syncer.log');
    }

    public function install() {
        $this->_executeQuery("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "multi_feed_syncer_logs` (
                `mfs_id` INT AUTO_INCREMENT,
                `mfsc_id` INT,
                `connection_status` TINYINT(1),
                `process_timing` INT,
                `process_data` TEXT,
                `process_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`mfs_id`),
                INDEX `mfsc_id` (`mfsc_id`),
                INDEX `connection_status` (`connection_status`),
                INDEX `process_timing` (`process_timing`),
                INDEX `process_date` (`process_date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ");

        $this->_executeQuery("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "multi_feed_syncer_connectors` (
                `mfsc_id` INT AUTO_INCREMENT,
                `connector` VARCHAR(32),
                `connector_key` VARCHAR(32) UNIQUE,
                `markup_percentage` DECIMAL(5,2) DEFAULT 0.00,
                PRIMARY KEY (`mfsc_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ");

        $query = $this->_executeQuery("SHOW COLUMNS FROM `" . DB_PREFIX . "multi_feed_syncer_connectors` LIKE 'markup_percentage'");
        if (!$query->num_rows) {
            $this->_executeQuery("ALTER TABLE `" . DB_PREFIX . "multi_feed_syncer_connectors` ADD `markup_percentage` DECIMAL(5,2) DEFAULT 0.00 AFTER `connector_key`");
        }

        $this->_executeQuery("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "product_image_download_queue` (
              `queue_id` int(11) NOT NULL AUTO_INCREMENT,
              `mfsc_id` int(11) NOT NULL,
              `product_id` int(11) NOT NULL,
              `image_url` TEXT NOT NULL,
              `is_main_image` tinyint(1) NOT NULL DEFAULT '0',
              `sort_order` int(3) NOT NULL DEFAULT '0',
              `status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending',
              `attempts` tinyint(1) NOT NULL DEFAULT '0',
              `date_added` datetime NOT NULL,
              PRIMARY KEY (`queue_id`),
              UNIQUE KEY `product_image_url` (`product_id`, `image_url`(255)),
              KEY `status_attempts` (`status`, `attempts`),
              KEY `mfsc_id` (`mfsc_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ");
    }

    public function uninstall() {
        $this->_executeQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "multi_feed_syncer_logs`;");
        $this->_executeQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "multi_feed_syncer_connectors`;");
        $this->_executeQuery("DROP TABLE IF EXISTS `" . DB_PREFIX . "product_image_download_queue`;");
    }

    public function getActivatedConnectors() {
        $query = $this->_executeQuery("SELECT * FROM `" . DB_PREFIX . "multi_feed_syncer_connectors` ORDER BY `connector` ASC");
        return $query->rows;
    }

    public function addConnector($data) {
        $this->_executeQuery("INSERT INTO " . DB_PREFIX . "multi_feed_syncer_connectors SET connector = '" . $this->db->escape($data['connector']) . "', connector_key = '" . $this->db->escape($data['connector_key']) . "'");
        return $this->db->getLastId();
    }
    
    public function getConnector($mfsc_id) {
        $query = $this->_executeQuery("SELECT * FROM " . DB_PREFIX . "multi_feed_syncer_connectors WHERE mfsc_id = '" . (int)$mfsc_id . "'");
        return $query->row;
    }

    /**
     * Взема данни за конектор по неговия ключ (connector_key).
     *
     * @param string $connector_key Ключът на конектора.
     * @return array|false Връща асоциативен масив с данните за конектора или false, ако не е намерен.
     */
    public function getConnectorByKey($connector_key) {
        $query = $this->_executeQuery("SELECT * FROM `" . DB_PREFIX . "multi_feed_syncer_connectors` WHERE `connector_key` = '" . $this->db->escape($connector_key) . "'");
        if ($query->num_rows) {
            return $query->row;
        } else {
            return false;
        }
    }

    public function getLogs($data = array()) {
        $sql = "SELECT mfs.*, mfc.connector FROM `" . DB_PREFIX . "multi_feed_syncer_logs` mfs LEFT JOIN `" . DB_PREFIX . "multi_feed_syncer_connectors` mfc ON (mfs.mfsc_id = mfc.mfsc_id)";
        
        $sql .= " ORDER BY mfs.process_date DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 10; // По подразбиране 10 лога на страница
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->_executeQuery($sql);
        return $query->rows;
    }

    public function getTotalLogs() {
        $query = $this->_executeQuery("SELECT COUNT(*) AS total FROM `" . DB_PREFIX . "multi_feed_syncer_logs`");
        return $query->row['total'];
    }
    
    public function addLog($log_data) {
        $this->_executeQuery("INSERT INTO " . DB_PREFIX . "multi_feed_syncer_logs SET 
            mfsc_id = '" . (int)$log_data['mfsc_id'] . "', 
            connection_status = '" . (int)$log_data['connection_status'] . "', 
            process_timing = '" . (int)$log_data['process_timing'] . "', 
            process_data = '" . $this->db->escape(isset($log_data['process_data']) ? serialize($log_data['process_data']) : '') . "', 
            process_date = NOW()");
        return $this->db->getLastId();
    }

    public function updateMarkupPercentage($mfsc_id, $markup) {
        $this->_executeQuery("UPDATE " . DB_PREFIX . "multi_feed_syncer_connectors SET markup_percentage = '" . (float)$markup . "' WHERE mfsc_id = '" . (int)$mfsc_id . "'");
    }

    /**
     * Обработва синхронизацията на продуктовите данни.
     * Този метод ще бъде описан подробно по-късно. [cite: 46]
     */
    public function setTestMode(bool $mode) {
        $this->test_mode = $mode;
        if ($this->test_mode) {
            $this->log->write("MultiFeed Syncer: ТЕСТОВ РЕЖИМ АКТИВИРАН.");
        } else {
            $this->log->write("MultiFeed Syncer: ТЕСТОВ РЕЖИМ ДЕАКТИВИРАН.");
        }
    }

    public function getTestMode() {
        return $this->test_mode;
    }

    /**
     * Добавя запис в лога за синхронизациите.
     * Използва полетата от таблицата oc_multi_feed_syncer_logs.
     *
     * @param int $mfsc_id ID на конектора
     * @param array $sync_stats Асоциативен масив със статистиките от синхронизацията
     * @param int $execution_time Време за изпълнение в секунди
     */
    public function addSynchronizationLog($mfsc_id, $sync_stats, $execution_time) {
        // Определяне на connection_status.
        // За момента, ако има грешки, приемаме, че connection_status е 0 (неуспех/проблем), иначе 1 (успех).
        $connection_status_val = (isset($sync_stats['errors']) && $sync_stats['errors'] > 0) ? 0 : 1;

        $sql = "INSERT INTO `" . DB_PREFIX . "multi_feed_syncer_logs` SET ";
        $sql .= "`mfsc_id` = " . (int)$mfsc_id . ", ";
        $sql .= "`connection_status` = " . (int)$connection_status_val . ", ";
        $sql .= "`process_timing` = " . (int)$execution_time . ", ";
        // Използваме json_encode за process_data, тъй като е по-стандартно и лесно за разчитане от serialize
        $sql .= "`process_data` = '" . $this->db->escape(json_encode($sync_stats)) . "', ";
        $sql .= "`process_date` = NOW()";
        
        // Използваме _executeQuery, за да сме съвместими с тестовия режим
        $this->_executeQuery($sql);
    }


    private function _executeQuery(string $sql, bool $do_log = true) {
        $trimmed_sql = trim($sql);
        // Извличане на първата дума от SQL заявката, за да се определи типът ѝ
        $sql_command_first_word = strtoupper(substr($trimmed_sql, 0, strpos($trimmed_sql, ' ') ?: strlen($trimmed_sql)));

        if ($this->test_mode) {
            // В тестов режим НЕ изпълняваме заявки, които променят данни (INSERT, UPDATE, DELETE, CREATE, ALTER, DROP)
            if (in_array($sql_command_first_word, ['INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP'])) {
                // if (isset($this->log)) { // Проверка дали $this->log е наличен
                if ($do_log)    
                    $this->writeToCronLog("MultiFeed Syncer (Test Mode SQL - SKIPPED EXECUTION): " . $sql);
                // }
                // Връщаме обект stdClass с num_rows = 0 и row = [], за да симулираме резултат от заявка без грешка, но без данни.
                // За INSERT може да се наложи да симулираме getLastId() по друг начин.
                $mock_result = new stdClass();
                $mock_result->num_rows = 0;
                $mock_result->row = [];
                $mock_result->rows = [];
                return $mock_result; // Връщаме обект, за да избегнем грешки при опит за достъп до num_rows
            } else if (in_array($sql_command_first_word, ['SELECT', 'SHOW'])) {
                // SELECT и SHOW заявките се изпълняват, но се логват.
                if ($do_log) $this->writeToCronLog("MultiFeed Syncer (Test Mode SQL - EXECUTED): " . $sql);
                return $this->db->query($sql);
            } else {
                // За други непознати типове заявки
                if ($do_log) $this->writeToCronLog("MultiFeed Syncer (Test Mode SQL - SKIPPED EXECUTION - Unhandled Type '" . $sql_command_first_word . "'): " . $sql);
                
                $mock_result = new stdClass(); // Подобно на горното
                $mock_result->num_rows = 0;
                $mock_result->row = [];
                $mock_result->rows = [];
                return $mock_result;
            }
        }
        
        // Ако не сме в тестов режим, изпълняваме всички заявки нормално
        return $this->db->query($sql);
    }

    private function _getLastId() {
        if ($this->test_mode) {
            // Тъй като INSERT заявките не се изпълняват реално, db->getLastId() няма да върне смислена стойност.
            // Генерираме уникално dummy ID.
            $dummy_id = $this->current_dummy_general_id_counter++;
            $this->writeToCronLog("MultiFeed Syncer (Test Mode): _getLastId() called, returning dummy ID: " . $dummy_id);
            return $dummy_id;
        }
        return $this->db->getLastId();
    }

    public function doSync($opencart_product_data, $mfsc_id) {

        $this->writeToCronLog("MultiFeed Syncer: Начало на синхронизацията за конектор ID: {$mfsc_id}.");

        $this->setTestMode(false);

        $start_time = microtime(true);
        // if (!isset($this->log) && class_exists('Log')) {
            $this->log = new Log('multi_feed_syncer_process.log'); // По-специфичен лог за процеса
        // }

        $this->writeToCronLog("MultiFeed Syncer: Начало на синхронизацията за конектор ID: {$mfsc_id}.");

        $processed_info = [
            'added' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => 0,
            'received' => count($opencart_product_data),
            'log_details' => [],
            'total_time_seconds' => 0
        ];
        

        if (empty($opencart_product_data)) {
            $processed_info['log_details'][] = "Няма продукти за синхронизиране.";
            $this->addSynchronizationLog($mfsc_id, $processed_info, 0);
            return $processed_info;
        }

        if($this->test_mode) {
            $this->writeToCronLog("MultiFeed Syncer (Test Mode): Начало на синхронизацията за конектор ID: {$mfsc_id}.");
        } else {
            $this->writeToCronLog("MultiFeed Syncer: Начало на синхронизацията за конектор ID: {$mfsc_id}.");
        }

    
        $default_language_id = (int)$this->config->get('config_language_id') ? (int)$this->config->get('config_language_id') : 1;
        $config_language = $this->config->get('config_language') ? $this->config->get('config_language') : 'bg';

        // $this->load->model('localisation/language');
        // $languages = $this->model_localisation_language->getLanguages();
        if (empty($languages)) { // Резервен вариант, ако не успее да зареди езиците
            $languages = [['language_id' => $default_language_id, 'code' => $config_language]];
        }

        // --- Начало: Зареждане на съществуващи SEO keywords ---
        $existing_seo_keywords = [];
        $query_keywords = $this->_executeQuery("SELECT LOWER(keyword) as keyword FROM `" . DB_PREFIX . "seo_url`");
        if ($query_keywords && $query_keywords->rows) {
            foreach ($query_keywords->rows as $row) {
                $existing_seo_keywords[$row['keyword']] = true;
            }
        }
        $this->writeToCronLog("MultiFeed Syncer: Заредени " . count($existing_seo_keywords) . " съществуващи SEO keywords в кеша.");
        // --- Край: Зареждане на съществуващи SEO keywords ---

        $query_markup = $this->_executeQuery("SELECT markup_percentage FROM " . DB_PREFIX . "multi_feed_syncer_connectors WHERE mfsc_id = '" . (int)$mfsc_id . "'");
        $markup_percentage = 0.0;
        if ($query_markup->num_rows && isset($query_markup->row['markup_percentage'])) {
            $markup_percentage = (float)$query_markup->row['markup_percentage'];
        } else {
            $processed_info['log_details'][] = "ПРЕДУПРЕЖДЕНИЕ: Процентна надценка (markup_percentage) не е намерена за конектор ID: {$mfsc_id}. Цените може да не са коректни.";
        }

        $identifier_field = 'sku'; 

        $product_codes_to_check = [];
        $valid_products_for_processing = []; 
 
        foreach ($opencart_product_data as $product_key => $product_data) {
            if (empty($product_data[$identifier_field])) {
                $processed_info['skipped']++;
                $processed_info['log_details'][] = "Продукт (#{$product_key}) е пропуснат поради липсващ идентификатор ('{$identifier_field}').";
                continue;
            }
            $sku = $product_data[$identifier_field];
            $product_codes_to_check[] = $this->db->escape($sku);
            
            // Вземане на името по подразбиране, за да се използва при генериране на SEO URL за съществуващи продукти
            $default_lang_name = 'N/A'; // Стойност по подразбиране, ако името не може да бъде намерено
            if (isset($product_data['product_description'][$default_language_id]['name']) && !empty(trim($product_data['product_description'][$default_language_id]['name'])) ) {
                $default_lang_name = $product_data['product_description'][$default_language_id]['name'];
            } elseif (isset($product_data['name']) && !empty(trim($product_data['name']))) { // Резервен вариант
                $default_lang_name = $product_data['name'];
            }
            $product_data['default_language_name'] = $default_lang_name;

            $valid_products_for_processing[$sku] = $product_data; 
        }
        unset($opencart_product_data); // Освобождаваме паметта от големия входен масив
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        $existing_products_map = [];
        if (!empty($product_codes_to_check)) {
            // Разделяне на заявката за съществуващи продукти на порции, ако е нужно
            $check_codes_chunks = array_chunk($product_codes_to_check, 500); // Порция от 500 SKU
            foreach ($check_codes_chunks as $chunk_of_codes) {
                if (empty($chunk_of_codes)) continue;
                $query_codes_string = "'" . implode("','", $chunk_of_codes) . "'";
                $query = $this->_executeQuery("SELECT `{$identifier_field}`, `product_id` FROM `" . DB_PREFIX . "product` WHERE `{$identifier_field}` IN (" . $query_codes_string . ")");
                if($query && $query->rows){
                    foreach ($query->rows as $row) {
                        $existing_products_map[$row[$identifier_field]] = $row['product_id'];
                    }
                }
            }
        }
        unset($product_codes_to_check, $check_codes_chunks); 

        $products_to_add = [];
        $products_to_update = []; 

        if ($this->test_mode) {
            $found_skus_log = [];
            $not_found_skus_log = [];
            foreach ($valid_products_for_processing as $product_code => $product_data_item) {
                if (isset($existing_products_map[$product_code])) {
                    $found_skus_log[] = $product_code . " (ID: " . $existing_products_map[$product_code] . ")";
                } else {
                    $not_found_skus_log[] = $product_code;
                }
            }
            if (isset($this->log)) { 
                $this->writeToCronLog("MultiFeed Syncer (Test Mode): Намерени съществуващи продукти (SKU -> ID):" . print_r($found_skus_log, true));
                $this->writeToCronLog("MultiFeed Syncer (Test Mode): НЕнамерени продукти (ще бъдат добавени като нови - SKU):" . print_r($not_found_skus_log, true));
            }
        }

        foreach ($valid_products_for_processing as $product_code => $product_data) {
            if (isset($existing_products_map[$product_code])) {
                $product_id = $existing_products_map[$product_code];
                $product_data['product_id'] = $product_id; 
                $products_to_update[$product_id] = $this->_filterProductDataForUpdate($product_data, $identifier_field);
            } else {
                $products_to_add[$product_code] = $product_data; 
            }
        }
        unset($valid_products_for_processing, $existing_products_map); 
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }


        if (!empty($products_to_add)) {
            // Предаваме $existing_seo_keywords по референция
            $this->_processProductsToInsert($products_to_add, $processed_info, $languages, $default_language_id, $markup_percentage, $identifier_field, $existing_seo_keywords, $mfsc_id);
        }
        unset($products_to_add); 
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        if (!empty($products_to_update)) {
            // Предаваме $existing_seo_keywords по референция
            $this->_processProductsToUpdate($products_to_update, $processed_info, $markup_percentage, $identifier_field, $existing_seo_keywords, $mfsc_id);
        }
        unset($products_to_update); 
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        $end_time = microtime(true);
        $processed_info['total_time_seconds'] = round($end_time - $start_time, 2);
        $processed_info['log_details'][] = "Синхронизацията приключи за {$processed_info['total_time_seconds']} секунди.";

        $this->addSynchronizationLog($mfsc_id, $processed_info, $processed_info['total_time_seconds']);
        
        $final_summary = "MultiFeed Syncer: Край на синхронизацията за конектор ID {$mfsc_id}. ";
        $final_summary .= "Получени: {$processed_info['received']}, Добавени: {$processed_info['added']}, Актуализирани: {$processed_info['updated']}, Пропуснати: {$processed_info['skipped']}, Грешки: {$processed_info['errors']}. ";
        $final_summary .= "Време: {$processed_info['total_time_seconds']} сек.";
        $this->writeToCronLog($final_summary);

        return $processed_info;
    }


    /**
     * Взема порция от задачи от опашката и ги маркира като 'processing', за да не ги вземе друг процес.
     * @param int $limit
     * @return array
     */
    public function getAndLockPendingImages($limit = 100) {

        // Първо, избираме ID-тата на задачите, които ще обработваме

        $sql = "SELECT `queue_id` FROM `" . DB_PREFIX . "product_image_download_queue` WHERE `status` = 'pending' AND `attempts` < 5 ORDER BY `queue_id` ASC LIMIT " . (int)$limit;

        $query = $this->_executeQuery($sql);

        // file_put_contents(DIR_LOGS . 'multi_feed_syncer_image_downloader.log', 'getAndLockPendingImages SQL: ' . $sql, FILE_APPEND);
        
        if (!$query->num_rows) {
            return [];
        }

        // file_put_contents(DIR_LOGS . 'multi_feed_syncer_image_downloader.log', 'getAndLockPendingImages: ' . print_r($query->rows, true), FILE_APPEND);
        
        $task_ids = array_column($query->rows, 'queue_id');
        $id_string = implode(',', $task_ids);

        // Заключваме тези редове, като сменяме статуса им
        $this->_executeQuery("UPDATE `" . DB_PREFIX . "product_image_download_queue` SET `status` = 'processing' WHERE `queue_id` IN (" . $id_string . ")");
        
        // Сега извличаме пълните данни за заключените задачи
        $full_task_query = $this->_executeQuery("SELECT * FROM `" . DB_PREFIX . "product_image_download_queue` WHERE `queue_id` IN (" . $id_string . ")");

        return $full_task_query->rows;
    }

    /**
     * Актуализира продуктите с новите пътища на изтеглените изображения.
     * @param array $processed_tasks Масив от задачи, съдържащи new_local_path.
     */
    public function updateProductsWithDownloadedImages(array $processed_tasks) {
        if (empty($processed_tasks)) {
            return;
        }
        
        $main_image_cases = [];
        $additional_images_by_product = [];

        foreach ($processed_tasks as $task) {
            if ($task['is_main_image']) {
                $main_image_cases[] = "WHEN " . (int)$task['product_id'] . " THEN '" . $this->db->escape($task['new_local_path']) . "'";
            } else {
                $additional_images_by_product[(int)$task['product_id']][] = [
                    'image' => $task['new_local_path'],
                    'sort_order' => $task['sort_order']
                ];
            }
        }

        // Пакетна актуализация на основното изображение в oc_product
        if (!empty($main_image_cases)) {
            $product_ids = array_unique(array_column($processed_tasks, 'product_id'));
            $sql_update_main = "UPDATE `" . DB_PREFIX . "product` SET `image` = (CASE `product_id` " . implode(" ", $main_image_cases) . " ELSE `image` END) WHERE `product_id` IN (" . implode(',', $product_ids) . ")";
            $this->_executeQuery($sql_update_main);
        }

        // Добавяне на допълнителните изображения
        if (!empty($additional_images_by_product)) {

            // 1. Вземаме ID-тата на продуктите, чиито допълнителни изображения ще бъдат заменени
            $product_ids_to_clear_images = array_keys($additional_images_by_product);

            // 2. Изтриваме ВСИЧКИ СТАРИ допълнителни изображения за тези продукти
            
            $this->_executeQuery("DELETE FROM `" . DB_PREFIX . "product_image` WHERE `product_id` IN (" . implode(',', $product_ids_to_clear_images) . ")");
        

            // 3. Подготвяме и вмъкваме новите
            $image_values = [];
            foreach ($additional_images_by_product as $product_id => $images) {
                foreach ($images as $img_data) {
                    $image_values[] = "(" . (int)$product_id . ", '" . $this->db->escape($img_data['image']) . "', " . (int)$img_data['sort_order'] . ")";
                }
            }

            if (!empty($image_values)) {
                // Вече можем да използваме чист INSERT, тъй като сме изтрили старите записи
                $sql_insert_additional = "INSERT INTO `" . DB_PREFIX . "product_image` (`product_id`, `image`, `sort_order`) VALUES " . implode(", ", $image_values);
                $this->_executeQuery($sql_insert_additional);
            }

        }
    }

    /**
     * Променя статуса на група задачи в опашката.
     * @param array $task_ids
     * @param string $status
     */
    public function updateQueueTasks(array $task_ids, string $status) {
        if (empty($task_ids)) {
            return;
        }
        $valid_statuses = ['completed', 'pending', 'failed'];
        if (!in_array($status, $valid_statuses)) {
            return;
        }
        $this->_executeQuery("UPDATE `" . DB_PREFIX . "product_image_download_queue` SET `status` = '" . $status . "' WHERE `queue_id` IN (" . implode(',', $task_ids) . ")");
    }

    /**
     * Увеличава броя опити за неуспешни задачи.
     * @param array $task_ids
     */
    public function incrementQueueTaskAttempts(array $task_ids) {
        if (empty($task_ids)) {
            return;
        }
        // Задачите се маркират като 'failed' и се увеличава броя опити.
        // Cron-ът ще ги пропусне, ако attempts >= 5.
        $this->_executeQuery("UPDATE `" . DB_PREFIX . "product_image_download_queue` SET `status` = 'failed', `attempts` = `attempts` + 1 WHERE `queue_id` IN (" . implode(',', $task_ids) . ")");
    }


    private function _processProductsToInsert(array $products_to_add_map, array &$processed_info, array $languages, int $default_language_id, float $markup_percentage, string $identifier_field, array &$existing_seo_keywords, int $mfsc_id) {
        $num_initially_to_add = count($products_to_add_map);
        $processed_info['log_details'][] = "Подготвени {$num_initially_to_add} продукта за пакетно добавяне.";

        try {
            // Предаваме $existing_seo_keywords
            $successfully_added_count_total = $this->_batchInsertProducts($products_to_add_map, $languages, $default_language_id, $markup_percentage, $identifier_field, $processed_info, $existing_seo_keywords, $mfsc_id);
            
            $processed_info['added'] += $successfully_added_count_total;

            if ($successfully_added_count_total < $num_initially_to_add) {
                $failed_count = $num_initially_to_add - $successfully_added_count_total;
                // Грешките се отчитат вътре в _batchInsertProducts, тук само добавяме обобщено съобщение
                $processed_info['log_details'][] = "{$failed_count} продукта не можаха да бъдат напълно добавени по време на _batchInsertProducts (вижте детайлните логове от метода).";
            }

        } catch (Exception $e) {
            $processed_info['errors'] += $num_initially_to_add; 
            $processed_info['log_details'][] = "Критична грешка по време на _processProductsToInsert (извън _batchInsertProducts): " . $e->getMessage();
        }
    }

    private function _processProductsToUpdate(array $products_to_update_map, array &$processed_info, float $markup_percentage, string $identifier_field, array &$existing_seo_keywords, int $mfsc_id) {
        $num_to_update = count($products_to_update_map);
        $processed_info['log_details'][] = "Подготвени {$num_to_update} продукта за пакетно актуализиране.";

        try {
            // Предаваме $processed_info и $existing_seo_keywords
            $updated_count = $this->_batchUpdateProducts($products_to_update_map, $markup_percentage, $identifier_field, $processed_info, $existing_seo_keywords, $mfsc_id);
            $processed_info['updated'] += $updated_count;
            // Грешките и детайлите се управляват вътре в _batchUpdateProducts
        } catch (Exception $e) {
            $processed_info['errors'] += $num_to_update;
            $processed_info['log_details'][] = "Критична грешка по време на _processProductsToUpdate: " . $e->getMessage();
        }
    }
        
    private function _filterProductDataForUpdate(array $product_data, string $identifier_field) {

        
        $filtered = [
            'product_id' => $product_data['product_id']
        ];
        if (array_key_exists('price', $product_data)) {
            $filtered['price'] = $product_data['price'];
        }
        if (array_key_exists('quantity', $product_data)) {
            $filtered['quantity'] = $product_data['quantity'];
        }
        if (array_key_exists('image', $product_data)) {
            $filtered['image'] = $product_data['image'];
        }
        if (array_key_exists('product_image', $product_data)) {
            $filtered['product_image'] = $product_data['product_image'];
        }
        
        // Включваме името на продукта от езика по подразбиране за генериране на SEO URL, ако е нужно
        if (isset($product_data['default_language_name'])) {
            $filtered['default_language_name'] = $product_data['default_language_name'];
        }

        // Включваме идентификатора за логване
        if (isset($product_data[$identifier_field])) {
            $filtered[$identifier_field] = $product_data[$identifier_field];
        } elseif (isset($product_data['sku'])) { // Резервен вариант, ако $identifier_field е различен от 'sku'
            $filtered['sku_for_log'] = $product_data['sku'];
        }
        return $filtered;
    }
    private function writeToCronLog($message) {
        $log_file = DIR_LOGS . 'multi_feed_syncer.log'; // [cite: 26]
        file_put_contents($log_file, $message . PHP_EOL, FILE_APPEND);
    }

    /**
     * Генерира SEO keyword от текст.
     * @param string $text
     * @return string
     */
    private function _generateSeoKeyword($text) {
        // Масив за транслитерация от кирилица към латиница
        $cyrillic = [
            'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd', 'е' => 'e', 'ж' => 'zh', 'з' => 'z',
            'и' => 'i', 'й' => 'y', 'к' => 'k', 'л' => 'l', 'м' => 'm', 'н' => 'n', 'о' => 'o', 'п' => 'p',
            'р' => 'r', 'с' => 's', 'т' => 't', 'у' => 'u', 'ф' => 'f', 'х' => 'h', 'ц' => 'ts', 'ч' => 'ch',
            'ш' => 'sh', 'щ' => 'sht', 'ъ' => 'a', 'ь' => 'y', 'ю' => 'yu', 'я' => 'ya',
            'А' => 'A', 'Б' => 'B', 'В' => 'V', 'Г' => 'G', 'Д' => 'D', 'Е' => 'E', 'Ж' => 'Zh', 'З' => 'Z',
            'И' => 'I', 'Й' => 'Y', 'К' => 'K', 'Л' => 'L', 'М' => 'M', 'Н' => 'N', 'О' => 'O', 'П' => 'P',
            'Р' => 'R', 'С' => 'S', 'Т' => 'T', 'У' => 'U', 'Ф' => 'F', 'Х' => 'H', 'Ц' => 'Ts', 'Ч' => 'Ch',
            'Ш' => 'Sh', 'Щ' => 'Sht', 'Ъ' => 'A', 'Ь' => 'Y', 'Ю' => 'Yu', 'Я' => 'Ya', ' ' => '-',
            'ьо' => 'yo', 'ЬО' => 'Yo', 'йо' => 'yo', 'ЙО' => 'Yo', 'дж' => 'dj', 'Дж' => 'Dj', 'ДЖ' => 'DJ'
        ];

        // Премахване на HTML тагове
        $text = strip_tags(html_entity_decode($text, ENT_QUOTES, 'UTF-8'));
        
        // Транслитерация на кирилица
        $text = str_replace(array_keys($cyrillic), array_values($cyrillic), $text);
        
        // Преобразуване в малки букви
        $text = mb_strtolower($text, 'UTF-8');
        
        // Замяна на не-буквено-цифрови символи с тирета
        $text = preg_replace('~[^\pL\d]+~u', '-', $text);
        
        // Премахване на начални и крайни тирета
        $text = trim($text, '-');
        
        // Премахване на повтарящи се тирета
        $text = preg_replace('~-+~', '-', $text);

        if (empty($text)) {
            return 'n-a-' . time(); // Резервен вариант, ако текстът е празен
        }

        return $text;
    }

    /**
     * Пакетно добавяне на нови продукти.
     * @param array $products_to_add Масив от продуктови данни [sku => product_data]
     * @param array $languages Масив с езиците от OpenCart
     * @param int $default_language_id ID на езика по подразбиране
     * @return array Мапинг на [sku => new_product_id]
     */
    private function _batchInsertProducts(array $products_to_add_map, array $languages, int $default_language_id, float $markup_percentage, string $identifier_field, array &$processed_info, array &$existing_seo_keywords, int $mfsc_id) {
        if (empty($products_to_add_map)) {
            return 0;
        }

        $successfully_added_main_product_count = 0;
        $product_chunks = array_chunk($products_to_add_map, self::MFS_BATCH_INSERT_CHUNK_SIZE, true); 
        
        $this->load->model('setting/setting'); 
        $default_stock_status_id = (int)$this->config->get('config_stock_status_id'); 
        $default_tax_class_id = 0; 
        $default_length_class_id = (int)$this->config->get('config_length_class_id');
        $default_weight_class_id = (int)$this->config->get('config_weight_class_id');
        $default_store_id = 0; 

        $chunk_number = 0;
        foreach ($product_chunks as $chunk_of_products_to_add) {
            $chunk_number++;
            $processed_info['log_details'][] = "Обработка на порция {$chunk_number}/" . count($product_chunks) . " за добавяне на продукти.";

            $product_values_chunk = [];
            $product_skus_in_chunk = []; 

            foreach ($chunk_of_products_to_add as $sku => $data) {
                $product_skus_in_chunk[] = $sku; 

                $image = '';
                $model = isset($data['model']) ? $this->db->escape($data['model']) : $this->db->escape($sku);
                $quantity = isset($data['quantity']) ? (int)$data['quantity'] : 0;
                $feed_price = isset($data['price']) ? (float)$data['price'] : 0.0;
                $price = round($feed_price * (1 + $markup_percentage / 100), 4);
                $stock_status_id = isset($data['stock_status_id']) ? (int)$data['stock_status_id'] : $default_stock_status_id;
                $manufacturer_id = isset($data['manufacturer_id']) ? (int)$data['manufacturer_id'] : 0;
                $shipping = isset($data['shipping']) ? (int)$data['shipping'] : 1;
                $tax_class_id = isset($data['tax_class_id']) ? (int)$data['tax_class_id'] : $default_tax_class_id;
                $date_available = isset($data['date_available']) ? $this->db->escape($data['date_available']) : date('Y-m-d');
                $weight = isset($data['weight']) ? (float)$data['weight'] : 0;
                $weight_class_id = isset($data['weight_class_id']) ? (int)$data['weight_class_id'] : $default_weight_class_id;
                $length = isset($data['length']) ? (float)$data['length'] : 0;
                $width = isset($data['width']) ? (float)$data['width'] : 0;
                $height = isset($data['height']) ? (float)$data['height'] : 0;
                $length_class_id = isset($data['length_class_id']) ? (int)$data['length_class_id'] : $default_length_class_id;
                $subtract = isset($data['subtract']) ? (int)$data['subtract'] : 1;
                $minimum = isset($data['minimum']) ? (int)$data['minimum'] : 1;
                $sort_order = isset($data['sort_order']) ? (int)$data['sort_order'] : 0;
                $status = 0;

                $product_values_chunk[] = "(
                    '" . $model . "', '" . $this->db->escape($sku) . "', 
                    '" . (isset($data['upc']) ? $this->db->escape($data['upc']) : '') . "', 
                    '" . (isset($data['ean']) ? $this->db->escape($data['ean']) : '') . "', 
                    '" . (isset($data['jan']) ? $this->db->escape($data['jan']) : '') . "', 
                    '" . (isset($data['isbn']) ? $this->db->escape($data['isbn']) : '') . "', 
                    '" . (isset($data['mpn']) ? $this->db->escape($data['mpn']) : '') . "', 
                    '" . (isset($data['location']) ? $this->db->escape($data['location']) : '') . "', 
                    " . $quantity . ", " . $stock_status_id . ", '" . $image . "', " . $manufacturer_id . ", 
                    " . $shipping . ", " . $price . ", " . (isset($data['points']) ? (int)$data['points'] : 0) . ", 
                    " . $tax_class_id . ", '" . $date_available . "', " . $weight . ", " . $weight_class_id . ", 
                    " . $length . ", " . $width . ", " . $height . ", " . $length_class_id . ", 
                    " . $subtract . ", " . $minimum . ", " . $sort_order . ", " . $status . ", 
                    NOW(), NOW()
                )";
            }

            $current_chunk_new_product_ids_map = []; 

            if (!empty($product_values_chunk)) {
                $sql_insert_product_chunk = "INSERT INTO `" . DB_PREFIX . "product` (
                    `model`, `sku`, `upc`, `ean`, `jan`, `isbn`, `mpn`, `location`, `quantity`, `stock_status_id`, `image`, `manufacturer_id`, 
                    `shipping`, `price`, `points`, `tax_class_id`, `date_available`, `weight`, `weight_class_id`, `length`, `width`, `height`, 
                    `length_class_id`, `subtract`, `minimum`, `sort_order`, `status`, `date_added`, `date_modified`
                ) VALUES " . implode(", ", $product_values_chunk);
                
                try {
                    $this->_executeQuery($sql_insert_product_chunk);
                    $num_in_chunk = count($product_skus_in_chunk);
                    
                    if ($this->test_mode) {
                        foreach ($product_skus_in_chunk as $original_sku_to_map) {
                            $dummy_product_id = $this->current_dummy_product_id_counter++;
                            $current_chunk_new_product_ids_map[$original_sku_to_map] = $dummy_product_id;
                            if (isset($this->log)) $this->log->write("MultiFeed Syncer (Test Mode): Присвоено dummy product_id {$dummy_product_id} за SKU: {$original_sku_to_map} в порция за добавяне.");
                        }
                        $successfully_added_main_product_count += $num_in_chunk;
                    } else {
                        $escaped_skus_for_query_chunk = [];
                        foreach($product_skus_in_chunk as $sku_in_chunk_orig) {
                            $escaped_skus_for_query_chunk[] = "'" . $this->db->escape($sku_in_chunk_orig) . "'";
                        }
                        
                        if (!empty($escaped_skus_for_query_chunk)) {
                            $query_new_ids_chunk = $this->_executeQuery("SELECT `product_id`, `sku` FROM `" . DB_PREFIX . "product` WHERE `sku` IN (" . implode(",", $escaped_skus_for_query_chunk) . ")");
                            if ($query_new_ids_chunk && $query_new_ids_chunk->rows) {
                                foreach ($query_new_ids_chunk->rows as $row) {
                                    $current_chunk_new_product_ids_map[$row['sku']] = (int)$row['product_id'];
                                    $successfully_added_main_product_count++;
                                }
                            }
                        }
                    }
                } catch (Exception $e) {
                    $processed_info['errors'] += count($product_values_chunk);
                    $processed_info['log_details'][] = "Грешка при INSERT в oc_product за порция {$chunk_number}: " . $e->getMessage() . " SQL: Частичен изглед - " . substr($sql_insert_product_chunk, 0, 255);
                    continue; 
                }
            }
            unset($product_values_chunk);

            if (!empty($current_chunk_new_product_ids_map)) {
                // Предаваме $existing_seo_keywords по референция
                $this->_insertRelatedProductDataForChunk($current_chunk_new_product_ids_map, $chunk_of_products_to_add, $languages, $default_language_id, $default_store_id, $processed_info, $chunk_number, $existing_seo_keywords, $mfsc_id);
            }
            unset($current_chunk_new_product_ids_map, $chunk_of_products_to_add);

            if (self::MFS_SLEEP_BETWEEN_CHUNKS > 0) {
                sleep(self::MFS_SLEEP_BETWEEN_CHUNKS);
            }
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
        }
        unset($product_chunks);
        return $successfully_added_main_product_count;
    }

    private function _insertRelatedProductDataForChunk(array $new_product_ids_map_chunk, array $original_data_chunk, array $languages, int $default_language_id, int $default_store_id, array &$processed_info, int $chunk_number, array &$existing_seo_keywords, int $mfsc_id) {
        $product_description_values_ch = [];
        $product_to_store_values_ch = [];
        $url_alias_values_ch = [];

        $images_queued_count = 0;

        foreach ($new_product_ids_map_chunk as $sku => $new_product_id) {
            if (!$new_product_id || !isset($original_data_chunk[$sku])) continue; 
            
            $product_data_item = $original_data_chunk[$sku];
            $product_to_store_values_ch[] = "(" . (int)$new_product_id . ", " . (int)$default_store_id . ")";

            $base_name_for_seo = '';
            foreach ($languages as $language) {
                $lang_id = (int)$language['language_id'];
                $name_val = "'N/A'"; // Обграждаме с кавички за SQL
                $description_val = "''"; // Празна стойност за SQL
                $meta_title_val = "'N/A'";
                
                $desc_data_source = null;
                if (isset($product_data_item['product_description'][$lang_id])) {
                    $desc_data_source = $product_data_item['product_description'][$lang_id];
                } elseif (isset($product_data_item['product_description'][$default_language_id])) { 
                    $desc_data_source = $product_data_item['product_description'][$default_language_id];
                } elseif (isset($product_data_item['name'])) { 
                     $desc_data_source = ['name' => $product_data_item['name'], 'description' => (isset($product_data_item['description']) ? $product_data_item['description'] : '')];
                }

                $current_name_unescaped = 'N/A';
                if ($desc_data_source) {
                    $current_name_unescaped = isset($desc_data_source['name']) ? trim($desc_data_source['name']) : 'N/A';
                    if (empty($current_name_unescaped)) $current_name_unescaped = 'N/A';
                    $name_val = "'" . $this->db->escape($current_name_unescaped) . "'";
                    
                    $description_val = "'" . $this->db->escape(isset($desc_data_source['description']) ? $desc_data_source['description'] : '') . "'";
                    
                    $current_meta_title_unescaped = isset($desc_data_source['meta_title']) && !empty(trim($desc_data_source['meta_title'])) ? trim($desc_data_source['meta_title']) : $current_name_unescaped;
                    if (empty($current_meta_title_unescaped)) $current_meta_title_unescaped = 'N/A';
                    $meta_title_val = "'" . $this->db->escape($current_meta_title_unescaped) . "'";
                }
                
                if ($lang_id == $default_language_id && $current_name_unescaped !== 'N/A') {
                    $base_name_for_seo = $current_name_unescaped;
                }

                $product_description_values_ch[] = "(
                    " . (int)$new_product_id . ", " . $lang_id . ", " . $name_val . ", 
                    " . $description_val . ", '' /* tag */, " . $meta_title_val . ", 
                    '' /* meta_description */, '' /* meta_keyword */
                )";
            }

            $main_image_url = !empty($product_data_item['image']) ? $product_data_item['image'] : null;

            // Основно изображение
            if ($main_image_url && filter_var($main_image_url, FILTER_VALIDATE_URL)) {
                $this->_queueImageForDownload($new_product_id, $main_image_url, true, 0, $mfsc_id);
                $images_queued_count++;
            }

            // Допълнителни изображения
            if (!empty($product_data_item['product_image'])) {
                foreach ($product_data_item['product_image'] as $img_data) {
                    // Проверяваме дали допълнителното изображение не е същото като основното
                    if (!empty($img_data['image']) && $img_data['image'] !== $main_image_url && filter_var($img_data['image'], FILTER_VALIDATE_URL)) {
                        $this->_queueImageForDownload($new_product_id, $img_data['image'], false, (int)$img_data['sort_order'], $mfsc_id);
                        $images_queued_count++;
                    }
                }
            }

            // Ако $base_name_for_seo все още е празен, опитваме с default_language_name от $product_data_item или SKU
            if (empty(trim($base_name_for_seo)) || $base_name_for_seo === 'N/A') {
                $base_name_for_seo = isset($product_data_item['default_language_name']) && trim($product_data_item['default_language_name']) !== 'N/A' && !empty(trim($product_data_item['default_language_name']))
                                     ? $product_data_item['default_language_name']
                                     : $sku;
            }
            
            if (!empty(trim($base_name_for_seo))) {
                $keyword = $this->_generateSeoKeyword( (string) $base_name_for_seo );
                $original_keyword = $keyword;
                $counter = 1;
                
                $lower_keyword = mb_strtolower($keyword, 'UTF-8');
                while (isset($existing_seo_keywords[$lower_keyword])) { // Проверка в кеша
                    $keyword = $original_keyword . '-' . $counter++;
                    $lower_keyword = mb_strtolower($keyword, 'UTF-8');
                }
                $url_alias_values_ch[] = "('product_id=" . (int)$new_product_id . "', '" . $this->db->escape($keyword) . "')";
                $existing_seo_keywords[$lower_keyword] = true; // Добавяме към кеша
            }
        }

        if ($images_queued_count > 0) {
            $processed_info['log_details'][] = "Добавени {$images_queued_count} изображения в опашката за изтегляне (порция за добавяне {$chunk_number}).";
        }

        try {
            if (!empty($product_description_values_ch)) {
                $sql_insert_desc_ch = "INSERT INTO `" . DB_PREFIX . "product_description` (`product_id`, `language_id`, `name`, `description`, `tag`, `meta_title`, `meta_description`, `meta_keyword`) VALUES " . implode(", ", $product_description_values_ch);
                $this->_executeQuery($sql_insert_desc_ch);
            }
            if (!empty($product_to_store_values_ch)) {
                $sql_insert_store_ch = "INSERT INTO `" . DB_PREFIX . "product_to_store` (`product_id`, `store_id`) VALUES " . implode(", ", $product_to_store_values_ch);
                $this->_executeQuery($sql_insert_store_ch);
            }
            if (!empty($url_alias_values_ch)) {
                $sql_insert_alias_ch = "INSERT INTO `" . DB_PREFIX . "seo_url` (`query`, `keyword`) VALUES " . implode(", ", $url_alias_values_ch);
                $this->_executeQuery($sql_insert_alias_ch);
            }
        } catch (Exception $e) {
            $processed_info['errors'] += count($new_product_ids_map_chunk); 
            $processed_info['log_details'][] = "Грешка при INSERT на свързани данни (description/store/alias) за порция {$chunk_number}: " . $e->getMessage();
        }
        unset($product_description_values_ch, $product_to_store_values_ch, $url_alias_values_ch, $original_data_chunk);
    }


    /**
     * Пакетно актуализира продукти: цена и количество се обновяват веднага,
     * а изображенията се поставят в опашка за асинхронно изтегляне.
     */
    private function _batchUpdateProducts(array $products_to_update_map, float $markup_percentage, string $identifier_field, array &$processed_info, array &$existing_seo_keywords, int $mfsc_id) {
        if (empty($products_to_update_map)) {
            return 0;
        }

        // 1. Извличаме предварително съществуващите локални изображения за всички продукти, които ще се актуализират.
        $product_ids_for_check = array_keys($products_to_update_map);
        $existing_main_images = [];
        $existing_additional_images = [];

        if (!empty($product_ids_for_check)) {
            // Вземаме основните изображения
            $query_main = $this->_executeQuery("SELECT product_id, image FROM `" . DB_PREFIX . "product` WHERE product_id IN (" . implode(',', $product_ids_for_check) . ")", $do_log = false);

            foreach ($query_main->rows as $row) {

                $image_valid = !empty($row['image']) && !filter_var($row['image'], FILTER_VALIDATE_URL);

                // $this->writeToCronLog("Validate main image: " . $row['image'] . " - " . json_encode($image_valid));

                // Записваме пътя, само ако не е празен и не е URL (т.е. е локален път)
                if ($image_valid) {
                    $existing_main_images[$row['product_id']] = $row['image'];
                }
            }

            unset($query_main);

            // Вземаме допълнителните изображения
            $query_additional = $this->_executeQuery("SELECT product_id FROM `" . DB_PREFIX . "product_image` WHERE product_id IN (" . implode(',', $product_ids_for_check) . ")", $do_log = false);
            foreach ($query_additional->rows as $row) {
                // Тук е достатъчно само да маркираме, че продуктът има поне едно допълнително изображение
                $existing_additional_images[$row['product_id']] = true;
            }

            unset($query_additional);
        }

        $this->writeToCronLog("Съществуващите локални изображения за всички продукти: " . print_r($existing_main_images, true));
        $this->writeToCronLog("Съществуващите допълнителни изображения за всички продукти: " . print_r($existing_additional_images, true));

        $product_chunks = array_chunk($products_to_update_map, self::MFS_BATCH_UPDATE_CHUNK_SIZE, true);
        $total_successfully_updated_count = 0;
        $images_queued_count = 0;

        $chunk_number = 0;
        foreach ($product_chunks as $chunk) {
            $chunk_number++;
            if (empty($chunk)) continue;

            $price_cases = [];
            $quantity_cases = [];

            foreach ($chunk as $product_id => $data) {
                // 1. Подготовка на данни за незабавна актуализация (цена и количество)
                if (array_key_exists('price', $data)) {
                    $calculated_price = round((float)$data['price'] * (1 + $markup_percentage / 100), 4);
                    $price_cases[] = "WHEN " . (int)$product_id . " THEN " . (float)$calculated_price;
                }
                if (array_key_exists('quantity', $data)) {
                    $quantity_cases[] = "WHEN " . (int)$product_id . " THEN " . (int)$data['quantity'];
                }

                // 2. Поставяне на изображенията в опашка за изтегляне
                $main_image_url = !empty($data['image']) ? $data['image'] : null;

                // Основно изображение: добавяме в опашката, само ако е URL И ако в базата няма вече локална снимка.
                if ($main_image_url && filter_var($main_image_url, FILTER_VALIDATE_URL) && empty($existing_main_images[$product_id])) {
                    $this->_queueImageForDownload($product_id, $main_image_url, true, 0, $mfsc_id);
                    $images_queued_count++;
                }
                
                // Допълнителни изображения: добавяме ги в опашката, само ако продуктът НЯМА никакви допълнителни изображения в базата.
                if (!empty($data['product_image']) && !isset($existing_additional_images[$product_id])) {
                    foreach ($data['product_image'] as $img_data) {
                        if (!empty($img_data['image']) && $img_data['image'] !== $main_image_url && filter_var($img_data['image'], FILTER_VALIDATE_URL)) {
                             $this->_queueImageForDownload($product_id, $img_data['image'], false, (int)$img_data['sort_order'], $mfsc_id);
                             $images_queued_count++;
                        }
                    }
                }
            }
            
            // Изпълняваме заявката САМО за цена и количество
            $affected_rows = $this->_executeUpdateProductQuery($chunk, $price_cases, $quantity_cases, [], $do_log = false); // Подаваме празен масив за изображения
            if ($affected_rows > 0) {
                 $processed_info['log_details'][] = "Засегнати {$affected_rows} продукта в основната таблица (порция {$chunk_number}).";
            }
            $total_successfully_updated_count += $affected_rows;
        }

        if ($images_queued_count > 0) {
            $processed_info['log_details'][] = "Общо {$images_queued_count} изображения са добавени в опашката за изтегляне.";
        }

        // 3. Генериране на липсващи SEO URL адреси (тази операция остава, тъй като е бърза)
        $this->_generateMissingSeoUrlsForUpdate($products_to_update_map, $identifier_field, $processed_info, $existing_seo_keywords);

        return $total_successfully_updated_count;
    }

    /**
     * Помощен метод за добавяне на URL на изображение в опашката за изтегляне.
     *
     * @param int $product_id
     * @param string $url
     * @param bool $is_main
     * @param int $sort_order
     * @param int $mfsc_id
     */
    private function _queueImageForDownload(int $product_id, string $url, bool $is_main, int $sort_order, int $mfsc_id) {
        $sql = "INSERT INTO `" . DB_PREFIX . "product_image_download_queue` SET 
                    `product_id` = " . $product_id . ",
                    `mfsc_id` = " . $mfsc_id . ",
                    `image_url` = '" . $this->db->escape($url) . "',
                    `is_main_image` = " . (int)$is_main . ",
                    `sort_order` = " . $sort_order . ",
                    `status` = 'pending',
                    `date_added` = NOW()
                ON DUPLICATE KEY UPDATE `status` = 'pending', `attempts` = 0, `date_added` = NOW()"; 
        // ON DUPLICATE KEY UPDATE е полезно, ако има UNIQUE индекс върху (product_id, image_url),
        // за да се избегнат дубликати и просто да се "рестартира" задачата.
        // За целта трябва да добавите UNIQUE INDEX `product_image_url` (`product_id`, `image_url`(255)) към таблицата.
        
        try {
            $this->_executeQuery($sql);
        } catch (Exception $e) {
            $this->log->write("ГРЕШКА при добавяне на изображение в опашката: " . $e->getMessage() . " SQL: " . $sql);
        }
    }

    private function _executeUpdateProductQuery(array $chunk, array $price_cases, array $quantity_cases, array $main_image_cases, bool $do_log = true) {
        $product_ids_in_chunk = array_keys($chunk);
        if (empty($product_ids_in_chunk)) {
            return 0;
        }

        $update_parts = [];
        if (!empty($price_cases)) {
            $update_parts[] = "`price` = (CASE `product_id` " . implode(" ", $price_cases) . " ELSE `price` END)";
        }
        if (!empty($quantity_cases)) {
            $update_parts[] = "`quantity` = (CASE `product_id` " . implode(" ", $quantity_cases) . " ELSE `quantity` END)";
        }
        if (!empty($main_image_cases)) {
            $update_parts[] = "`image` = (CASE `product_id` " . implode(" ", $main_image_cases) . " ELSE `image` END)";
        }

        if (empty($update_parts)) {
            return 0;
        }

        $sql = "UPDATE `" . DB_PREFIX . "product` SET " . implode(", ", $update_parts) . ", `date_modified` = NOW() WHERE `product_id` IN (" . implode(",", $product_ids_in_chunk) . ")";

        try {
            $this->_executeQuery($sql, $do_log);
            return $this->test_mode ? count($product_ids_in_chunk) : $this->db->countAffected();
        } catch (Exception $e) {
            $this->log->write("Грешка при UPDATE на продукти: " . $e->getMessage());
            return 0;
        }
    }

    private function _generateMissingSeoUrlsForUpdate(array $products_to_update_map, string $identifier_field, array &$processed_info, array &$existing_seo_keywords) {
        $product_ids_to_check = array_keys($products_to_update_map);
        if (empty($product_ids_to_check)) {
            return;
        }

        // Намираме кои от тези продукти вече имат SEO URL
        $product_ids_with_seo_url = [];
        $seo_check_chunks = array_chunk($product_ids_to_check, 200);
        foreach ($seo_check_chunks as $id_chunk) {
            $query_parts = array_map(function($pid) { return "'product_id=" . (int)$pid . "'"; }, $id_chunk);
            $seo_query = $this->_executeQuery("SELECT `query` FROM `" . DB_PREFIX . "seo_url` WHERE `query` IN (" . implode(",", $query_parts) . ")");
            if ($seo_query && $seo_query->rows) {
                foreach ($seo_query->rows as $row) {
                    if (preg_match('/product_id=(\d+)/', $row['query'], $matches)) {
                        $product_ids_with_seo_url[(int)$matches[1]] = true;
                    }
                }
            }
        }

        // Намираме продуктите БЕЗ SEO URL и генерираме за тях
        $new_url_alias_values = [];
        foreach ($products_to_update_map as $product_id => $data) {
            if (!isset($product_ids_with_seo_url[$product_id])) {
                $sku_for_log = isset($data[$identifier_field]) ? $data[$identifier_field] : (isset($data['sku_for_log']) ? $data['sku_for_log'] : 'ID:'.$product_id);
                $base_name = isset($data['default_language_name']) && $data['default_language_name'] !== 'N/A' ? $data['default_language_name'] : $sku_for_log;

                if (!empty(trim($base_name))) {
                    $keyword = $this->_generateSeoKeyword((string)$base_name);
                    $original_keyword = $keyword;
                    $counter = 1;
                    $lower_keyword = mb_strtolower($keyword, 'UTF-8');

                    while (isset($existing_seo_keywords[$lower_keyword])) {
                        $keyword = $original_keyword . '-' . $counter++;
                        $lower_keyword = mb_strtolower($keyword, 'UTF-8');
                    }
                    $new_url_alias_values[] = "('product_id=" . (int)$product_id . "', '" . $this->db->escape($keyword) . "')";
                    $existing_seo_keywords[$lower_keyword] = true; // Добавяме към кеша, за да избегнем дублиране в рамките на същата синхронизация
                    $processed_info['log_details'][] = "Генериран нов SEO URL '{$keyword}' за съществуващ продукт ID {$product_id}.";
                }
            }
        }
        
        // Вмъкваме новите SEO URL-и на порции
        if (!empty($new_url_alias_values)) {
            $seo_insert_chunks = array_chunk($new_url_alias_values, 100);
            foreach ($seo_insert_chunks as $seo_chunk) {
                $sql_insert_alias = "INSERT INTO `" . DB_PREFIX . "seo_url` (`query`, `keyword`) VALUES " . implode(", ", $seo_chunk);
                $this->_executeQuery($sql_insert_alias);
            }
        }
    }
}
?>
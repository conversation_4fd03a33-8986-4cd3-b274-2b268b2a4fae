<?php

// --- Начало на Функции за Разработчици ---

// Дефинирайте IP адресите на разработчиците тук
// Може да е един IP адрес или масив от IP адреси
define('DEVELOPER_IPS', ['**************', '::1']); // Добавен е и localhost IPv6 за локална разработка

/**
 * Проверява дали текущата заявка е от IP адрес на разработчик.
 * Отчита и CLI контекста, където IP адресът не е релевантен.
 *
 * @return bool True, ако заявката е от разработчик или е CLI (за cron задачи), false в противен случай.
 */
function isDeveloper() {
    // За cron задачи (CLI), можем да приемем, че е "developer mode" или да добавим друга логика
    if (php_sapi_name() === 'cli') {
        // За CLI може да искате винаги да логвате с LogDeveloper,
        // или да имате отделна настройка за "developer mode" за cron.
        // Засега ще върнем true, за да може LogDeveloper да работи и при cron, ако е необходимо.
        // Ако искате да е false за CLI, променете го.
        return true; 
    }

    if (!empty($_SERVER['REMOTE_ADDR']) && defined('DEVELOPER_IPS')) {
        $remote_addr = $_SERVER['REMOTE_ADDR'];
        if (is_array(DEVELOPER_IPS)) {
            return in_array($remote_addr, DEVELOPER_IPS);
        } else {
            return $remote_addr === DEVELOPER_IPS;
        }
    }
    return false;
}

/**
 * Записва съобщение в специален лог файл за разработчици,
 * само ако заявката е от IP адрес на разработчик или е CLI.
 *
 * @param mixed $message Съобщението за запис (може да е низ, масив или обект).
 * @param string $log_file Името на лог файла (по подразбиране 'developer_log.txt').
 */
function LogDeveloper($message, $log_file = 'developer_log.txt') {
    if (isDeveloper()) {
        $log_path = DIR_LOGS . $log_file; // DIR_LOGS е дефинирана Opencart константа
        
        $formatted_message = "[" . date("Y-m-d H:i:s") . "] ";
        if (is_array($message) || is_object($message)) {
            $formatted_message .= print_r($message, true);
        } else {
            $formatted_message .= $message;
        }
        $formatted_message .= PHP_EOL;
        
        file_put_contents($log_path, $formatted_message, FILE_APPEND);
    }
}

function Log_($message, $log_file = 'multi_feed_syncer_.log') {

    $log_path = DIR_LOGS . $log_file; // DIR_LOGS е дефинирана Opencart константа
    
    $formatted_message = "[" . date("Y-m-d H:i:s") . "] ";
    if (is_array($message) || is_object($message)) {
        $formatted_message .= print_r($message, true);
    } else {
        $formatted_message .= $message;
    }
    $formatted_message .= PHP_EOL;
    
    file_put_contents($log_path, $formatted_message, FILE_APPEND);

}



// --- Край на Функции за Разработчици ---